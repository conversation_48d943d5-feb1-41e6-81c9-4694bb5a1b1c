import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'reserve_screen.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/core/services/service_locator.dart';

class PlaceDetailsScreen extends StatefulWidget {
  final Map<String, dynamic>? placeData;
  final int? placeId;
  final PlaceDetailModel? placeDetail;

  const PlaceDetailsScreen({
    super.key,
    this.placeData,
    this.placeId,
    this.placeDetail,
  }) : assert(placeData != null || placeId != null || placeDetail != null,
            'Either placeData, placeId, or placeDetail must be provided');

  @override
  State<PlaceDetailsScreen> createState() => _PlaceDetailsScreenState();
}

class _PlaceDetailsScreenState extends State<PlaceDetailsScreen> {
  bool isFavorite = false;
  bool showFullDescription = false;
  PlaceDetailModel? _placeDetail;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    if (widget.placeDetail != null) {
      _placeDetail = widget.placeDetail;
      isFavorite = _placeDetail!.favorite;
    } else if (widget.placeId != null) {
      _loadPlaceDetails();
    } else if (widget.placeData != null) {
      // Convert legacy placeData to PlaceDetailModel for compatibility
      _convertLegacyData();
    }
  }

  Future<void> _loadPlaceDetails() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final apiService = getIt<PropertiesApiService>();
      final placeDetail = await apiService.getPlaceDetails(widget.placeId!);
      setState(() {
        _placeDetail = placeDetail;
        isFavorite = placeDetail.favorite;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _convertLegacyData() {
    // Convert legacy Map data to PlaceDetailModel for backward compatibility
    final data = widget.placeData!;
    _placeDetail = PlaceDetailModel(
      id: data['id'] ?? 0,
      cityId: data['city_id'] ?? 0,
      title: data['title'] ?? '',
      content: data['description'] ?? data['content'] ?? '',
      image: data['image'],
      video: data['video'],
      price: double.tryParse(data['price']?.toString() ?? '0') ?? 0.0,
      weekendPrice: data['weekend_price'] != null
          ? double.tryParse(data['weekend_price'].toString())
          : null,
      weekPrice: data['week_price'] != null
          ? double.tryParse(data['week_price'].toString())
          : null,
      monthPrice: data['month_price'] != null
          ? double.tryParse(data['month_price'].toString())
          : null,
      lat: data['lat'] != null ? double.tryParse(data['lat'].toString()) : null,
      lon: data['lon'] != null ? double.tryParse(data['lon'].toString()) : null,
      confirmation: data['confirmation'] ?? 0,
      active: data['active'] == 1 || data['active'] == true,
      serviceCategoryId: data['service_category_id'] ?? 0,
      serviceCategoryFormId: data['service_category_form_id'],
      userId: data['user_id'] ?? 0,
      views: data['views'] ?? 0,
      createdAt: data['created_at'] ?? '',
      updatedAt: data['updated_at'] ?? '',
      deletedAt: data['deleted_at'],
      rating: data['rating'] != null || data['reviews'] != null
          ? double.tryParse(
              (data['rating'] ?? data['reviews'])?.toString() ?? '0')
          : null,
      noOfRates: data['no_of_rates'],
      noGuests: data['no_guests'] ?? data['guests'],
      beds: data['beds'] ?? data['bedrooms'],
      baths: data['baths'] ?? data['bathrooms'],
      bookingRules: data['booking_rules'],
      cancelationRules: data['cancelation_rules'],
      includeCommissionDaily: data['include_commission_daily'] ?? 0,
      includeCommissionWeekly: data['include_commission_weekly'] ?? 0,
      includeCommissionMonthly: data['include_commission_monthly'] ?? 0,
      favorite: data['favorite'] ?? false,
      gallery: (data['gallery'] as List<dynamic>?)
              ?.map((item) => GalleryItemModel.fromJson(item))
              .toList() ??
          [],
      facilities: (data['facilities'] as List<dynamic>?)
              ?.map((item) => FacilityItemModel.fromJson(item))
              .toList() ??
          [],
      country: data['country'] ?? '',
      city: data['city'] ?? '',
      hoster: HosterModel.fromJson(data['hoster'] ?? {}),
    );
    isFavorite = _placeDetail!.favorite;
  }

  String getValidImageUrl(String? url) {
    if (url == null || url.isEmpty || url == 'null') {
      return 'https://placehold.co/400x300';
    }
    return url;
  }

  void _showGallery(BuildContext context) {
    if (_placeDetail!.gallery.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.black,
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.8,
          child: PageView.builder(
            itemCount: _placeDetail!.gallery.length,
            itemBuilder: (context, index) {
              final galleryItem = _placeDetail!.gallery[index];
              return Stack(
                children: [
                  Center(
                    child: Image.network(
                      galleryItem.image,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) =>
                          const Icon(Icons.error, color: Colors.white),
                    ),
                  ),
                  Positioned(
                    top: 40,
                    right: 20,
                    child: IconButton(
                      icon: const Icon(Icons.close,
                          color: Colors.white, size: 30),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                  Positioned(
                    bottom: 20,
                    left: 20,
                    right: 20,
                    child: Text(
                      '${index + 1} من ${_placeDetail!.gallery.length}',
                      style: const TextStyle(color: Colors.white),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    if (_isLoading) {
      return Scaffold(
        backgroundColor: context.backgroundColor,
        appBar: AppBar(
          backgroundColor: context.backgroundColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back_ios_rounded,
                color: context.primaryTextColor),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_error != null) {
      return Scaffold(
        backgroundColor: context.backgroundColor,
        appBar: AppBar(
          backgroundColor: context.backgroundColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back_ios_rounded,
                color: context.primaryTextColor),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline,
                  size: 64, color: context.secondaryTextColor),
              const SizedBox(height: 16),
              Text(
                'حدث خطأ في تحميل البيانات',
                style: AppTextStyles.font18Bold
                    .copyWith(color: context.primaryTextColor),
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: AppTextStyles.font14Regular
                    .copyWith(color: context.secondaryTextColor),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  if (widget.placeId != null) {
                    _loadPlaceDetails();
                  }
                },
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    if (_placeDetail == null) {
      return Scaffold(
        backgroundColor: context.backgroundColor,
        appBar: AppBar(
          backgroundColor: context.backgroundColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back_ios_rounded,
                color: context.primaryTextColor),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: const Center(child: Text('لا توجد بيانات متاحة')),
      );
    }

    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: CustomScrollView(
        slivers: [
          // Enhanced Hero Image Section with App Bar
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            backgroundColor: context.backgroundColor,
            elevation: 0,
            systemOverlayStyle: context.isDarkMode
                ? SystemUiOverlayStyle.light
                : SystemUiOverlayStyle.dark,
            leading: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.cardColor.withValues(alpha: 0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: Icon(
                  Icons.arrow_back_ios_rounded,
                  color: context.primaryTextColor,
                ),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.cardColor.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: Icon(
                    Icons.share_rounded,
                    color: context.primaryTextColor,
                  ),
                  onPressed: () {},
                ),
              ),
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.cardColor.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: Icon(
                    isFavorite
                        ? Icons.favorite_rounded
                        : Icons.favorite_border_rounded,
                    color: isFavorite ? Colors.red : context.primaryTextColor,
                  ),
                  onPressed: () {
                    setState(() {
                      isFavorite = !isFavorite;
                    });
                  },
                ),
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  GestureDetector(
                    onTap: () => _showGallery(context),
                    child: Image.network(
                      getValidImageUrl(_placeDetail!.gallery.isNotEmpty
                          ? _placeDetail!.gallery.first.image
                          : _placeDetail!.image),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        color:
                            context.secondaryTextColor.withValues(alpha: 0.1),
                        child: Icon(
                          Icons.image_not_supported_rounded,
                          size: 64,
                          color: context.secondaryTextColor,
                        ),
                      ),
                    ),
                  ),
                  // Gradient overlay for better text readability
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.3),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Enhanced Content
          SliverToBoxAdapter(
            child: Container(
              color: context.backgroundColor,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Enhanced Title and Location
                    _buildTitleSection(s),
                    const SizedBox(height: 24),

                    // Enhanced Quick Stats
                    _buildQuickStats(s),
                    const SizedBox(height: 24),

                    // Enhanced Host Information
                    _buildHostSection(s),
                    const SizedBox(height: 24),

                    // Enhanced Description
                    _buildDescriptionSection(s),
                    const SizedBox(height: 24),

                    // Enhanced Amenities
                    _buildAmenitiesSection(s),
                    const SizedBox(height: 24),

                    // Enhanced Location
                    _buildLocationSection(s),
                    const SizedBox(height: 24),

                    // Enhanced Reviews
                    _buildReviewsSection(s),
                    const SizedBox(height: 100), // Space for bottom booking bar
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBookingBottomBar(s),
    );
  }

  Widget _buildTitleSection(S s) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _placeDetail!.title,
            style: AppTextStyles.font24Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.location_on_rounded,
                size: 18,
                color: context.accentColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "${_placeDetail!.city} - ${_placeDetail!.country}",
                  style: AppTextStyles.font16Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              if (_placeDetail!.rating != null) ...[
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.star_rounded,
                          size: 16, color: Colors.amber),
                      const SizedBox(width: 4),
                      Text(
                        _placeDetail!.rating?.toStringAsFixed(1) ?? '0',
                        style: AppTextStyles.font14SemiBold.copyWith(
                          color: Colors.amber[800],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '(${_placeDetail!.noOfRates ?? 0} تقييم)',
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.secondaryTextColor,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ],
              const Spacer(),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: context.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${_placeDetail!.price.toStringAsFixed(0)} ر.س/ليلة',
                  style: AppTextStyles.font16Bold.copyWith(
                    color: context.accentColor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(S s) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.propertyDetails,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              if (_placeDetail!.beds != null)
                _buildStatItem(
                    Icons.bed_rounded, '${_placeDetail!.beds}', s.bedrooms),
              if (_placeDetail!.baths != null)
                _buildStatItem(Icons.bathtub_rounded, '${_placeDetail!.baths}',
                    s.bathrooms),
              if (_placeDetail!.noGuests != null)
                _buildStatItem(Icons.people_rounded,
                    '${_placeDetail!.noGuests}', s.guests),
              // Check if WiFi facility exists
              if (_placeDetail!.facilities.any((facility) =>
                  facility.title.toLowerCase().contains('wifi') ||
                  facility.title.toLowerCase().contains('واي فاي')))
                _buildStatItem(Icons.wifi_rounded, '', 'واي فاي مجاني'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, String label) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: context.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            size: 24,
            color: context.accentColor,
          ),
        ),
        const SizedBox(height: 8),
        if (value.isNotEmpty)
          Text(
            value,
            style: AppTextStyles.font16Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
        Text(
          label,
          style: AppTextStyles.font12Regular.copyWith(
            color: context.secondaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildHostSection(S s) {
    final hoster = _placeDetail!.hoster;
    final registeredDate = DateTime.tryParse(hoster.registeredSince);
    final yearsHosting = registeredDate != null
        ? DateTime.now().difference(registeredDate).inDays ~/ 365
        : 0;

    return EnhancedCard(
      child: Row(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundColor: context.secondaryTextColor.withValues(alpha: 0.1),
            child: Icon(
              Icons.person_rounded,
              color: context.secondaryTextColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مستضاف من قبل ${hoster.name}',
                  style: AppTextStyles.font18Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  yearsHosting > 0
                      ? '$yearsHosting ${yearsHosting == 1 ? 'سنة' : 'سنوات'} في الاستضافة'
                      : 'مستضيف جديد',
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          if (hoster.rating != null && hoster.rating! >= 4.0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.amber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.star, size: 14, color: Colors.amber),
                  const SizedBox(width: 4),
                  Text(
                    hoster.rating!.toStringAsFixed(1),
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: Colors.amber[800],
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection(S s) {
    final description = _placeDetail!.content.isNotEmpty
        ? _placeDetail!.content
        : 'لا يوجد وصف متاح.';
    final shouldShowMore = description.length > 200;
    final displayText = showFullDescription || !shouldShowMore
        ? description
        : '${description.substring(0, 200)}...';

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.aboutThisPlace,
            style: AppTextStyles.font20Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            displayText,
            style: AppTextStyles.font16Regular.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
          if (shouldShowMore) ...[
            const SizedBox(height: 8),
            GestureDetector(
              onTap: () {
                setState(() {
                  showFullDescription = !showFullDescription;
                });
              },
              child: Text(
                showFullDescription ? s.showLess : s.showMore,
                style: AppTextStyles.font16SemiBold.copyWith(
                  color: context.accentColor,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAmenitiesSection(S s) {
    final facilities = _placeDetail!.facilities;

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.whatThisPlaceOffers,
            style: AppTextStyles.font20Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          if (facilities.isEmpty)
            Text(
              s.noAmenitiesListed,
              style: AppTextStyles.font16Regular.copyWith(
                color: context.secondaryTextColor,
              ),
            )
          else
            ...facilities.take(6).map((facility) => Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: facility.icon != null
                            ? Image.network(
                                facility.icon!,
                                width: 16,
                                height: 16,
                                color: Colors.green,
                                errorBuilder: (context, error, stackTrace) =>
                                    const Icon(Icons.check_rounded,
                                        size: 16, color: Colors.green),
                              )
                            : const Icon(Icons.check_rounded,
                                size: 16, color: Colors.green),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          facility.title,
                          style: AppTextStyles.font16Regular.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                      ),
                      if (facility.count > 0)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${facility.count}',
                            style: AppTextStyles.font12SemiBold.copyWith(
                              color: context.accentColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                )),
          if (facilities.length > 6) ...[
            const SizedBox(height: 16),
            EnhancedButton(
              text: '${s.showAllAmenities} (${facilities.length})',
              onPressed: () {
                // Show all amenities dialog
              },
              isOutlined: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLocationSection(S s) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.whereYoullBe,
            style: AppTextStyles.font20Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            "${_placeDetail!.city}, ${_placeDetail!.country}",
            style: AppTextStyles.font16Regular.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: context.secondaryTextColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.map_rounded,
                    size: 48,
                    color: context.accentColor,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    s.mapView,
                    style: AppTextStyles.font16Regular.copyWith(
                      color: context.accentColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsSection(S s) {
    final rating = _placeDetail!.rating ?? 0.0;
    final noOfRates = _placeDetail!.noOfRates ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.star, size: 24, color: Colors.amber),
            const SizedBox(width: 8),
            Text(
              '$rating',
              style: AppTextStyles.font20Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '($noOfRates ${s.reviews})',
              style: AppTextStyles.font16Regular.copyWith(
                color: context.secondaryTextColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Sample review
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: Colors.grey[300],
                    child:
                        const Icon(Icons.person, size: 20, color: Colors.grey),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'تقييم الضيف',
                          style: AppTextStyles.font16SemiBold.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                        Text(
                          'مارس 2024',
                          style: AppTextStyles.font14Regular.copyWith(
                            color: context.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: List.generate(
                        5,
                        (index) => Icon(
                              index < rating ? Icons.star : Icons.star_border,
                              size: 16,
                              color: Colors.amber,
                            )),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'مكان رائع للإقامة! نظيف ومريح وكما هو موصوف تماماً. المضيف كان متجاوباً ومفيداً جداً.',
                style: AppTextStyles.font14Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        OutlinedButton(
          onPressed: () {
            // Show all reviews
          },
          style: OutlinedButton.styleFrom(
            side: const BorderSide(color: Colors.black),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            'عرض جميع ${s.reviews} ($noOfRates)',
            style: AppTextStyles.font16Medium.copyWith(
              color: context.primaryTextColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBookingBottomBar(S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      text: '${_placeDetail!.price.toStringAsFixed(0)} ر.س ',
                      style: AppTextStyles.font20Bold.copyWith(
                        color: context.primaryTextColor,
                      ),
                      children: [
                        TextSpan(
                          text: 'ليلة',
                          style: AppTextStyles.font16Regular.copyWith(
                            color: context.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 4),
                  if (_placeDetail!.rating != null)
                    Row(
                      children: [
                        const Icon(Icons.star, size: 16, color: Colors.amber),
                        const SizedBox(width: 4),
                        Text(
                          _placeDetail!.rating!.toStringAsFixed(1),
                          style: AppTextStyles.font14SemiBold.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[400],
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ReserveScreen(
                        placeTitle: _placeDetail!.title,
                        policy: _placeDetail!.cancelationRules ?? '',
                        pricePerNight: _placeDetail!.price,
                      ),
                    ),
                  );
                },
                child: Text(
                  s.reserve,
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ✅ Widget مخصص للأيقونة والنص
class FeatureIcon extends StatelessWidget {
  final IconData icon;
  final String label;

  const FeatureIcon({super.key, required this.icon, required this.label});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Icon(icon, size: 30),
        const SizedBox(height: 8),
        Text(label,
            style: AppTextStyles.font12Regular.copyWith(
              color: Colors.grey[600],
            )),
      ],
    );
  }
}
